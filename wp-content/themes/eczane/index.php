<?php
/**
 * The main template file
 */

get_header(); ?>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <?php
                // Get real statistics from eczane posts and ilce taxonomy
                $today = date('Y-m-d');

                // Count districts (ilce terms)
                $district_terms = get_terms(array(
                    'taxonomy' => 'ilce',
                    'hide_empty' => true
                ));
                $district_count = is_array($district_terms) ? count($district_terms) : 0;

                // Count today's eczane posts
                $today_eczaneler = get_posts(array(
                    'post_type' => 'eczane',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => 'duty_date',
                            'value' => $today,
                            'compare' => '='
                        )
                    ),
                    'post_status' => 'publish'
                ));
                $today_eczane_count = count($today_eczaneler);

                // Count total eczane posts
                $total_eczaneler = get_posts(array(
                    'post_type' => 'eczane',
                    'posts_per_page' => -1,
                    'post_status' => 'publish'
                ));
                $total_eczane_count = count($total_eczaneler);
                ?>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $district_count ?: '39'; ?></div>
                    <div class="stat-label">İlçe Kapsamı</div>
                    <div class="stat-desc">İstanbul Geneli</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $today_eczane_count ?: '97'; ?></div>
                    <div class="stat-label">Bugün Nöbetçi</div>
                    <div class="stat-desc">Aktif Eczane</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Kesintisiz Hizmet</div>
                    <div class="stat-desc">Güncel Veriler</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-sync-alt"></i>
                    <h3>Anlık Güncellenen Veriler</h3>
                </div>
                <div class="feature-item">
                    <i class="fas fa-phone"></i>
                    <h3>Telefon ve Adres Bilgileri</h3>
                </div>
                <div class="feature-item">
                    <i class="fas fa-route"></i>
                    <h3>Çoklu Navigasyon Seçenekleri</h3>
                </div>
                <div class="feature-item">
                    <i class="fas fa-user-friends"></i>
                    <h3>Kullanıcı Dostu Deneyim</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content Sections -->
    <main class="main-content">
        <div class="container">
            <div class="content-grid">
                <!-- Nearest Pharmacies -->
                <section class="content-section" id="pharmacies">
                    <div class="section-header">
                        <i class="fas fa-map-marker-alt"></i>
                        <h2>En Yakın Eczaneler</h2>
                    </div>
                    <p>Size en yakın nöbetçi eczaneleri anında bulun ve hemen yol tarifi alın.</p>
                    <div class="pharmacy-list">
                        <?php
                        // Get today's eczane posts for display
                        $today = date('Y-m-d');

                        $recent_eczaneler = get_posts(array(
                            'post_type' => 'eczane',
                            'posts_per_page' => 6,
                            'meta_query' => array(
                                array(
                                    'key' => 'duty_date',
                                    'value' => $today,
                                    'compare' => '='
                                )
                            ),
                            'orderby' => 'rand',
                            'post_status' => 'publish'
                        ));

                        if ($recent_eczaneler):
                            foreach ($recent_eczaneler as $eczane):
                                $address = get_post_meta($eczane->ID, 'address', true);
                                $phone = get_post_meta($eczane->ID, 'phone', true);
                                $districts = wp_get_post_terms($eczane->ID, 'ilce');
                                $district_name = !empty($districts) ? $districts[0]->name : '';
                        ?>
                        <div class="pharmacy-item">
                            <div class="pharmacy-info">
                                <h3><?php echo esc_html($eczane->post_title); ?></h3>
                                <p><i class="fas fa-map-marker-alt"></i> <?php echo esc_html($address); ?><?php if ($district_name): ?>, <?php echo esc_html($district_name); ?><?php endif; ?></p>
                                <?php if ($phone): ?>
                                <p><i class="fas fa-phone"></i> <?php echo esc_html($phone); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="pharmacy-actions">
                                <button class="btn-directions" onclick="getDirections('<?php echo esc_js($address); ?>')"><i class="fas fa-directions"></i> Yol Tarifi</button>
                                <?php if ($phone): ?>
                                <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $phone)); ?>" class="btn-call"><i class="fas fa-phone"></i> Ara</a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php
                            endforeach;
                        else:
                        ?>
                        <div class="pharmacy-item">
                            <div class="pharmacy-info">
                                <h3>Veri Yükleniyor...</h3>
                                <p><i class="fas fa-info-circle"></i> Nöbetçi eczane verileri güncelleniyor.</p>
                                <p><a href="<?php echo admin_url('admin.php?page=eczane-management'); ?>">Veri İçe Aktar</a></p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <a href="#" class="view-more">Haritada Gör <i class="fas fa-arrow-right"></i></a>
                </section>

                <!-- AdSense Sidebar -->
                <aside class="sidebar-ad">
                    <div class="ad-placeholder vertical">
                        <!-- Google AdSense Sidebar Ad -->
                        <div class="ad-content">Reklam Alanı<br>300x600</div>
                    </div>
                </aside>

                <!-- Duty Pharmacy Map -->
                <section class="content-section" id="map">
                    <div class="section-header">
                        <i class="fas fa-map"></i>
                        <h2>Nöbetçi Eczane Haritası</h2>
                    </div>
                    <p>İstanbul'daki tüm nöbetçi eczaneleri harita üzerinde görüntüleyin.</p>
                    <div class="map-container">
                        <div class="interactive-map">
                            <div class="map-placeholder">
                                <i class="fas fa-map"></i>
                                <p>İnteraktif Harita Yükleniyor...</p>
                            </div>
                        </div>
                    </div>
                    <a href="#" class="view-more">Haritaya Git <i class="fas fa-arrow-right"></i></a>
                </section>

                <!-- Districts List -->
                <section class="content-section" id="districts">
                    <div class="section-header">
                        <i class="fas fa-list"></i>
                        <h2>İlçelere Göre Liste</h2>
                    </div>
                    <p>Nöbetçi eczaneleri ilçe bazlı listelerin ve detaylı bilgilere ulaşın.</p>
                    <div class="districts-grid">
                        <?php
                        // Get districts from ilce taxonomy with today's eczane count
                        $today = date('Y-m-d');
                        $districts = get_terms(array(
                            'taxonomy' => 'ilce',
                            'hide_empty' => true,
                            'number' => 12,
                            'orderby' => 'count',
                            'order' => 'DESC'
                        ));

                        if ($districts && !is_wp_error($districts)):
                            foreach ($districts as $district):
                                // Get today's eczane count for this district
                                $today_count = get_posts(array(
                                    'post_type' => 'eczane',
                                    'posts_per_page' => -1,
                                    'tax_query' => array(
                                        array(
                                            'taxonomy' => 'ilce',
                                            'field' => 'term_id',
                                            'terms' => $district->term_id,
                                        ),
                                    ),
                                    'meta_query' => array(
                                        array(
                                            'key' => 'duty_date',
                                            'value' => $today,
                                            'compare' => '='
                                        )
                                    ),
                                    'post_status' => 'publish',
                                    'fields' => 'ids'
                                ));
                                $count = is_array($today_count) ? count($today_count) : 0;

                                // Debug output
                                echo "<!-- DEBUG: District: " . $district->name . ", Count: " . $count . " -->";

                                // Show all districts (temporarily for debugging)
                                // if ($count > 0):
                        ?>
                        <a href="<?php echo get_term_link($district); ?>" class="district-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?php echo esc_html($district->name); ?></span>
                            <small><?php echo $count; ?> Eczane</small>
                        </a>
                        <?php
                                // endif;
                            endforeach;
                        else:
                        ?>
                        <div class="district-item">
                            <i class="fas fa-info-circle"></i>
                            <span>İlçe verileri yükleniyor...</span>
                            <small><a href="<?php echo admin_url('admin.php?page=eczane-management'); ?>">Veri İçe Aktar</a></small>
                        </div>
                        <?php endif; ?>
                    </div>
                    <a href="<?php echo home_url('/ilce/'); ?>" class="view-more">Tüm İlçeleri Gör <i class="fas fa-arrow-right"></i></a>
                </section>
            </div>
        </div>
    </main>

<?php get_footer(); ?>
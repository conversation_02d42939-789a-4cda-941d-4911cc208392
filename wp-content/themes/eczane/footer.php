    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <div class="logo-icon">E</div>
                        <div class="logo-text">
                            <h3>İstanbul Nöbetçi Eczaneleri</h3>
                            <p>Güvenilir ve güncel nöbetçi eczane bilgileri</p>
                        </div>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Hızlı Erişim</h4>
                    <ul>
                        <li><a href="#map">Harita</a></li>
                        <li><a href="#pharmacies">Eczaneler</a></li>
                        <li><a href="#districts">İlçeler</a></li>
                        <li><a href="#faq">SSS</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Bilgi</h4>
                    <ul>
                        <li><a href="#">Hakkımızda</a></li>
                        <li><a href="#">İletişim</a></li>
                        <li><a href="#">Gizlilik Politikası</a></li>
                        <li><a href="#">Kullanım Şartları</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Bizi Takip Edin</h4>
                    <div class="social-links">
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 İstanbul Nöbetçi Eczaneleri. Tüm hakları saklıdır.</p>
                <p>Veriler İstanbul Eczacı Odası'ndan alınmaktadır.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileToggle && mobileMenu) {
                mobileToggle.addEventListener('click', function() {
                    mobileMenu.classList.toggle('active');
                });
            }

            // Search functionality
            const searchInput = document.getElementById('district-search');
            const searchBtn = document.querySelector('.search-btn');

            if (searchInput && searchBtn) {
                searchBtn.addEventListener('click', function() {
                    const query = searchInput.value.trim();
                    if (query) {
                        // Simple search - redirect to district page if exact match
                        const slug = query.toLowerCase()
                            .replace(/ç/g, 'c')
                            .replace(/ğ/g, 'g')
                            .replace(/ı/g, 'i')
                            .replace(/ö/g, 'o')
                            .replace(/ş/g, 's')
                            .replace(/ü/g, 'u')
                            .replace(/[^a-z0-9]/g, '');

                        window.location.href = '<?php echo home_url(); ?>/ilce/' + slug + '/';
                    }
                });

                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchBtn.click();
                    }
                });
            }

            // Location button
            const locationBtn = document.querySelector('.location-btn');
            if (locationBtn) {
                locationBtn.addEventListener('click', function() {
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(function(position) {
                            // For now, just show an alert
                            alert('Konum özelliği yakında eklenecek!');
                        });
                    } else {
                        alert('Tarayıcınız konum özelliğini desteklemiyor.');
                    }
                });
            }
        });

        // Directions function
        function getDirections(address) {
            const encodedAddress = encodeURIComponent(address);
            const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
            window.open(mapsUrl, '_blank');
        }
    </script>

    <?php wp_footer(); ?>
</body>
</html>

<?php
/**
 * District Taxonomy Template
 * Template for displaying pharmacies in a specific district
 */

get_header(); ?>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <?php
            $current_term = get_queried_object();
            $district_name = $current_term->name;
            $today = date('Y-m-d');

            // Debug output
            echo "<!-- DEBUG: Current term: " . print_r($current_term, true) . " -->";
            echo "<!-- DEBUG: District name: " . $district_name . " -->";
            echo "<!-- DEBUG: Today: " . $today . " -->";

            // Get today's pharmacies for this district
            $today_pharmacies = get_posts(array(
                'post_type' => 'eczane',
                'posts_per_page' => -1,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'ilce',
                        'field' => 'term_id',
                        'terms' => $current_term->term_id,
                    ),
                ),
                'meta_query' => array(
                    array(
                        'key' => 'duty_date',
                        'value' => $today,
                        'compare' => '='
                    )
                ),
                'meta_key' => 'duty_date',
                'orderby' => 'title',
                'order' => 'ASC'
            ));

            // Get tomorrow's pharmacies
            $tomorrow = date('Y-m-d', strtotime('+1 day'));
            $tomorrow_pharmacies = get_posts(array(
                'post_type' => 'eczane',
                'posts_per_page' => -1,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'ilce',
                        'field' => 'term_id',
                        'terms' => $current_term->term_id,
                    ),
                ),
                'meta_query' => array(
                    array(
                        'key' => 'duty_date',
                        'value' => $tomorrow,
                        'compare' => '='
                    )
                ),
                'meta_key' => 'duty_date',
                'orderby' => 'title',
                'order' => 'ASC'
            ));

            // Debug output for query results
            echo "<!-- DEBUG: Today pharmacies count: " . count($today_pharmacies) . " -->";
            echo "<!-- DEBUG: Tomorrow pharmacies count: " . count($tomorrow_pharmacies) . " -->";
            if (!empty($today_pharmacies)) {
                echo "<!-- DEBUG: First today pharmacy: " . print_r($today_pharmacies[0], true) . " -->";
            }
            ?>

            <!-- District Header -->
            <section class="district-header">
                <div class="district-info">
                    <h1><i class="fas fa-map-marker-alt"></i> <?php echo esc_html($district_name); ?> Nöbetçi Eczaneleri</h1>
                    <p class="district-desc">
                        <?php echo esc_html($district_name); ?> ilçesindeki nöbetçi eczanelerin güncel listesi.
                        Bugün ve yarın nöbetçi olan eczaneleri aşağıda bulabilirsiniz.
                    </p>
                </div>
                <div class="district-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($today_pharmacies); ?></div>
                        <div class="stat-label">Bugün Nöbetçi</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($tomorrow_pharmacies); ?></div>
                        <div class="stat-label">Yarın Nöbetçi</div>
                    </div>
                </div>
            </section>

            <!-- Today's Pharmacies -->
            <?php if ($today_pharmacies): ?>
            <section class="pharmacy-section">
                <h2><i class="fas fa-calendar-day"></i> Bugün Nöbetçi Eczaneler (<?php echo date('d.m.Y'); ?>)</h2>
                <div class="pharmacy-grid">
                    <?php foreach ($today_pharmacies as $pharmacy):
                        $address = get_post_meta($pharmacy->ID, 'address', true);
                        $phone = get_post_meta($pharmacy->ID, 'phone', true);
                    ?>
                    <div class="pharmacy-card">
                        <div class="pharmacy-header">
                            <h3><?php echo esc_html($pharmacy->post_title); ?></h3>
                            <span class="duty-badge today">Bugün Nöbetçi</span>
                        </div>
                        <div class="pharmacy-details">
                            <p class="address">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php echo esc_html($address); ?>
                            </p>
                            <?php if ($phone): ?>
                            <p class="phone">
                                <i class="fas fa-phone"></i>
                                <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $phone)); ?>">
                                    <?php echo esc_html($phone); ?>
                                </a>
                            </p>
                            <?php endif; ?>
                        </div>
                        <div class="pharmacy-actions">
                            <button class="btn-directions" onclick="getDirections('<?php echo esc_js($address); ?>')">
                                <i class="fas fa-directions"></i> Yol Tarifi
                            </button>
                            <?php if ($phone): ?>
                            <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $phone)); ?>" class="btn-call">
                                <i class="fas fa-phone"></i> Ara
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>

            <!-- Tomorrow's Pharmacies -->
            <?php if ($tomorrow_pharmacies): ?>
            <section class="pharmacy-section">
                <h2><i class="fas fa-calendar-plus"></i> Yarın Nöbetçi Eczaneler (<?php echo date('d.m.Y', strtotime('+1 day')); ?>)</h2>
                <div class="pharmacy-grid">
                    <?php foreach ($tomorrow_pharmacies as $pharmacy):
                        $address = get_post_meta($pharmacy->ID, 'address', true);
                        $phone = get_post_meta($pharmacy->ID, 'phone', true);
                    ?>
                    <div class="pharmacy-card">
                        <div class="pharmacy-header">
                            <h3><?php echo esc_html($pharmacy->post_title); ?></h3>
                            <span class="duty-badge tomorrow">Yarın Nöbetçi</span>
                        </div>
                        <div class="pharmacy-details">
                            <p class="address">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php echo esc_html($address); ?>
                            </p>
                            <?php if ($phone): ?>
                            <p class="phone">
                                <i class="fas fa-phone"></i>
                                <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $phone)); ?>">
                                    <?php echo esc_html($phone); ?>
                                </a>
                            </p>
                            <?php endif; ?>
                        </div>
                        <div class="pharmacy-actions">
                            <button class="btn-directions" onclick="getDirections('<?php echo esc_js($address); ?>')">
                                <i class="fas fa-directions"></i> Yol Tarifi
                            </button>
                            <?php if ($phone): ?>
                            <a href="tel:<?php echo esc_attr(preg_replace('/[^\d+]/', '', $phone)); ?>" class="btn-call">
                                <i class="fas fa-phone"></i> Ara
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>

            <!-- No Data Message -->
            <?php if (!$today_pharmacies && !$tomorrow_pharmacies): ?>
            <section class="no-data">
                <div class="no-data-content">
                    <i class="fas fa-info-circle"></i>
                    <h3>Veri Bulunamadı</h3>
                    <p><?php echo esc_html($district_name); ?> ilçesi için henüz nöbetçi eczane verisi bulunmuyor.</p>
                    <a href="<?php echo home_url(); ?>" class="btn-primary">Ana Sayfaya Dön</a>
                </div>
            </section>
            <?php endif; ?>

        </div>
    </main>

    <script>
        function getDirections(address) {
            const encodedAddress = encodeURIComponent(address);
            const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
            window.open(mapsUrl, '_blank');
        }
    </script>

<?php get_footer(); ?>

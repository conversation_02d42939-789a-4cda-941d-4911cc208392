# Translation of Themes - Twenty Twenty-Three in Turkish
# This file is distributed under the same license as the Themes - Twenty Twenty-Three package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-28 12:57:00+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: tr\n"
"Project-Id-Version: Themes - Twenty Twenty-Three\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three is designed to take advantage of the new design tools introduced in WordPress 6.1. With a clean, blank base as a starting point, this default theme includes ten diverse style variations created by members of the WordPress community. Whether you want to build a complex or incredibly simple website, you can do it quickly and intuitively through the bundled styles or dive into creation and full customization yourself."
msgstr "<PERSON><PERSON><PERSON>, WordPress 6.1'de tanıtılan yeni tasarım araçlarından yararlanmak için tasarlanmıştır. Başlangıç noktası olarak temiz, boş bir tabana sahip bu varsayılan tema, WordPress topluluğu üyeleri tarafından oluşturulan on farklı stil varyasyonunu içerir. İster karmaşık ister inanılmaz derecede basit bir web sitesi oluşturmak isteyin, bunu birlikte verilen stiller aracılığıyla hızlı ve sezgisel bir şekilde yapabilir veya oluşturma ve tam özelleştirmeye kendiniz dalabilirsiniz."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three"
msgstr "Yirmi Yirmi-Üç"

#: theme.json
msgctxt "Template part name"
msgid "Comments Template Part"
msgstr "Yorum şablon parçası"

#. Translators: WordPress link.
#: patterns/footer-default.php:20
msgid "Proudly powered by %s"
msgstr "%s gururla sunar"

#: patterns/hidden-404.php:13
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "Sistem yazı tipi"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Primary"
msgstr "Birincil"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr "İkincil"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "Üçüncül"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "Boş"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Üst kısım"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Alt kısım"

#: theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: theme.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: theme.json
msgctxt "Custom template name"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Blog (Alternative)"
msgstr "Blog (Alternatif)"

#: styles/whisper.json
msgctxt "Style variation name"
msgid "Whisper"
msgstr "Fısıltı"

#: styles/sherbet.json
msgctxt "Style variation name"
msgid "Sherbet"
msgstr "Şerbet"

#: styles/pitch.json
msgctxt "Font size name"
msgid "2X Large"
msgstr "2X büyük"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Çok büyük"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Large"
msgstr "Büyük"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Orta"

#: styles/pitch.json
msgctxt "Font size name"
msgid "small"
msgstr "küçük"

#: styles/pitch.json
msgctxt "Space size name"
msgid "7"
msgstr "7"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Dots"
msgstr "Noktalar"

#: styles/pilgrimage.json
msgctxt "Style variation name"
msgid "Pilgrimage"
msgstr "Hac"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Gigantic"
msgstr "Devasa"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Huge"
msgstr "Kocaman"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Normal"
msgstr "Normal"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Tiny"
msgstr "Minik"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/marigold.json
msgctxt "Style variation name"
msgid "Marigold"
msgstr "Marigold"

#: styles/grapes.json
msgctxt "Style variation name"
msgid "Grapes"
msgstr "Üzüm"

#: styles/electric.json
msgctxt "Style variation name"
msgid "Electric"
msgstr "Elektrik"

#: styles/canary.json
msgctxt "Style variation name"
msgid "Canary"
msgstr "Kanarya"

#: styles/block-out.json styles/canary.json styles/pilgrimage.json
#: styles/sherbet.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "Varsayılan filtre"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Temel"

#: styles/aubergine.json
msgctxt "Style variation name"
msgid "Aubergine"
msgstr "Patlıcan"

#: patterns/post-meta.php:65
msgctxt "Label for a list of post tags"
msgid "Tags:"
msgstr "Etiketler:"

#: patterns/hidden-no-results.php:10
msgctxt "Message explaining that there are no results returned from a search"
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Üzgünüz, ancak arama terimlerinizle hiçbir şey eşleşmedi. Lütfen farklı anahtar kelimelerle tekrar deneyin."

#: patterns/hidden-comments.php:13
msgctxt "Title of comments section"
msgid "Comments"
msgstr "Yorumlar"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgid "Search"
msgstr "Arama"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "placeholder for search field"
msgid "Search..."
msgstr "Arama..."

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "label"
msgid "Search"
msgstr "Arama"

#: patterns/hidden-404.php:19
msgctxt "Message to convey that a webpage could not be found"
msgid "This page could not be found."
msgstr "Bu sayfa bulunamadı."

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Yazı meta bilgileri"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary to Primary Fixed"
msgstr "Üçüncüden ikinciye, ikinciden sabit birinciye"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary Fixed"
msgstr "Birinciden ikinciye, ikinciden sabit üçüncüye"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary"
msgstr "Birinciden ikinciye, ikinciden üçüncüye"

#: styles/pitch.json
msgctxt "Style variation name"
msgid "Pitch"
msgstr "Zift"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Base to Primary"
msgstr "Temelden birinciye"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary"
msgstr "Üçüncüden ikinciye"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Secondary to Primary"
msgstr "İkinciden birinciye"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Primary to Secondary"
msgstr "Birinciden ikinciye"

#: styles/block-out.json
msgctxt "Style variation name"
msgid "Block out"
msgstr "Durdurma"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Primary to Tertiary"
msgstr "Birinciden üçüncüye"

#: styles/aubergine.json styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Primary"
msgstr "Üçüncüden birinciye"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Base to Secondary to Base"
msgstr "Temelden ikinciye, ikinciden temele"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Secondary to Base"
msgstr "İkinciden temele"

#: patterns/post-meta.php
msgctxt "Pattern title"
msgid "Post Meta"
msgstr "Yazı metası"

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "Hidden No Results Content"
msgstr "Gizli sonuç yok içeriği"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Hidden Comments"
msgstr "Gizli yorumlar"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "Hidden 404"
msgstr "Gizli 404"

#: patterns/footer-default.php
msgctxt "Pattern title"
msgid "Default Footer"
msgstr "Varsayılan alt kısım"

#: patterns/call-to-action.php:25
msgctxt "sample content for call to action button"
msgid "Get In Touch"
msgstr "İletişime geçin"

#: patterns/call-to-action.php:16
msgctxt "sample content for call to action"
msgid "Got any book recommendations?"
msgstr "Kitap tavsiyeleriniz var mı?"

#: patterns/call-to-action.php
msgctxt "Pattern title"
msgid "Call to action"
msgstr "Eyleme geçme çağrısı"

#: patterns/post-meta.php:29
msgctxt "Verb to explain the publication status of a post"
msgid "Posted"
msgstr "Yayımlandı"

#: patterns/post-meta.php:49
msgctxt "Preposition to show the relationship between the post and its author"
msgid "by"
msgstr "yazarı:"

#: patterns/post-meta.php:37
msgctxt "Preposition to show the relationship between the post and its categories"
msgid "in"
msgstr "kategorisi"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress ekibi"

#. Author URI of the theme
#: style.css patterns/footer-default.php:21
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://wordpress.org"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentythree"
msgstr "https://wordpress.org/themes/twentytwentythree"
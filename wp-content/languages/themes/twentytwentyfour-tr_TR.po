# Translation of Themes - Twenty Twenty-Four in Turkish
# This file is distributed under the same license as the Themes - Twenty Twenty-Four package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-06 09:29:39+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: tr\n"
"Project-Id-Version: Themes - Twenty Twenty-Four\n"

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four"
msgstr "Yirmi <PERSON>-<PERSON>"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website. Its collection of templates and patterns tailor to different needs, such as presenting a business, blogging and writing or showcasing work. A multitude of possibilities open up with just a few adjustments to color and typography. Twenty Twenty-Four comes with style variations and full page designs to help speed up the site building process, is fully compatible with the site editor, and takes advantage of new design tools introduced in WordPress 6.4."
msgstr "Yirmi <PERSON>rt esnek, çok yönlü ve her web sitesine uygulanabilecek şekilde tasarlanmıştır. Bir işletmeyi tanıtmak, blog ve yazı yazmak veya çalışmaları sergilemek gibi farklı ihtiyaçlara göre uyarlanabilen bir şablon ve model koleksiyonudur. Renk ve tipografide yalnızca birkaç ayarlamayla çok sayıda olasılık ortaya çıkar. Yirmi Yirmi-Dört, site oluşturma sürecini hızlandırmaya yardımcı olmak için biçem çeşitleri ve tam sayfa tasarımlar ile birlikte gelir, site düzenleyiciyle tamamen uyumludur ve WordPress 6.4'te sunulan yeni tasarım araçlarından yararlanır."

#: patterns/footer.php:96
msgid "Twitter/X"
msgstr "Twitter/X"

#: patterns/footer.php:51
msgid "Careers"
msgstr "Kariyer"

#: patterns/footer.php:50
msgid "History"
msgstr "Tarihçe"

#: patterns/footer.php:49
msgid "Team"
msgstr "Ekip"

#: patterns/footer.php:74
msgid "Contact Us"
msgstr "Bize ulaşın"

#: patterns/footer.php:73
msgid "Terms and Conditions"
msgstr "Şartlar ve koşullar"

#: patterns/footer.php:72
msgid "Privacy Policy"
msgstr "Gizlilik politikası"

#: functions.php:199
msgctxt "Block pattern category"
msgid "Pages"
msgstr "Sayfalar"

#: patterns/posts-list.php
msgctxt "Pattern title"
msgid "List of posts without images, 1 column"
msgstr "Görselsiz yazı listesi, 1 sütun"

#: patterns/hidden-portfolio-hero.php:16
msgid "I’m <em>Leia Acosta</em>, a passionate photographer who finds inspiration in capturing the fleeting beauty of life."
msgstr "Ben <em>Leia Acosta</em>, hayatın geçici güzelliğini yakalayarak ilham bulan tutkulu bir fotoğrafçıyım."

#: functions.php:200
msgid "A collection of full page layouts."
msgstr "Tam sayfa düzenlerinden oluşan bir koleksiyon."

#. Author URI of the theme
#. Translators: WordPress link.
#: style.css patterns/footer-centered-logo-nav.php:22
#: patterns/footer-colophon-3-col.php:91 patterns/footer.php:117
msgid "https://wordpress.org"
msgstr "https://wordpress.org"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Üst kısım"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Alt kısım"

#: styles/fossil.json styles/ice.json theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Çok büyük"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "Büyük"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Orta"

#: theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Temel"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Yazı meta bilgileri"

#: functions.php:111
msgid "With asterisk"
msgstr "Yıldız işareti ile"

#: functions.php:93
msgid "With arrow"
msgstr "Ok ile"

#: functions.php:74
msgid "Checkmark"
msgstr "Onay işareti"

#: functions.php:51
msgid "Pill"
msgstr "Hap"

#: functions.php:28
msgid "Arrow icon"
msgstr "Ok simgesi"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Kenar çubuğu"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "Küçük"

#: patterns/text-title-left-image-right.php:35
msgctxt "Call to Action button text"
msgid "About us"
msgstr "Hakkımızda"

#: patterns/text-feature-grid-3-col.php:84
msgctxt "Sample feature heading"
msgid "Consulting"
msgstr "Müşavirlik"

#: patterns/text-faq.php:15
msgctxt "Heading of the FAQs"
msgid "FAQs"
msgstr "SSS"

#: patterns/text-faq.php:12
msgctxt "Name of the FAQ pattern"
msgid "FAQs"
msgstr "SSS"

#: patterns/text-faq.php
msgctxt "Pattern title"
msgid "FAQ"
msgstr "SSS"

#: patterns/team-4-col.php:97
msgctxt "Sample role of a team member"
msgid "Architect"
msgstr "Mimar"

#: patterns/page-newsletter-landing.php:40
msgctxt "Sample content for newsletter subscribe button"
msgid "Sign up"
msgstr "Üye ol"

#: patterns/page-about-business.php
msgctxt "Pattern title"
msgid "About"
msgstr "Hakkında"

#: patterns/hidden-sidebar.php:75
msgctxt "search form placeholder"
msgid "Search..."
msgstr "Ara..."

#: patterns/hidden-sidebar.php:49
msgid "Useful Links"
msgstr "İşe yarar bağlantılar"

#: patterns/hidden-sidebar.php:33
msgid "Popular Categories"
msgstr "Popüler kategoriler"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Kenar çubuğu"

#: patterns/hidden-search.php:9
msgctxt "search button text"
msgid "Search"
msgstr "Ara"

#: patterns/hidden-search.php:9 patterns/hidden-sidebar.php:75
msgctxt "search form label"
msgid "Search"
msgstr "Ara"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Ara"

#: patterns/hidden-post-navigation.php:12
msgctxt "Label before the title of the next post. There is a space after the colon."
msgid "Next: "
msgstr "Sonraki: "

#: patterns/hidden-post-navigation.php:11
msgctxt "Label before the title of the previous post. There is a space after the colon."
msgid "Previous: "
msgstr "Önceki: "

#: patterns/hidden-post-navigation.php:9 patterns/hidden-post-navigation.php:10
#: patterns/hidden-posts-heading.php:10
#: patterns/template-index-portfolio.php:16
msgid "Posts"
msgstr "Yazılar"

#: patterns/hidden-post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Yazı dolaşımı"

#: patterns/hidden-no-results.php:9
msgctxt "Message explaining that there are no results returned from a search"
msgid "No posts were found."
msgstr "İçerik bulunamadı."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "No results"
msgstr "Sonuç yok"

#: patterns/hidden-comments.php:12
msgid "Comments"
msgstr "Yorumlar"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Yorumlar"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/footer.php:92
msgid "Social Media"
msgstr "Sosyal medya"

#: patterns/footer.php:86
msgid "Social"
msgstr "Sosyal"

#: patterns/footer.php:64 patterns/footer.php:70
msgid "Privacy"
msgstr "Gizlilik"

#: patterns/footer.php:41 patterns/footer.php:47
msgid "About"
msgstr "Hakkında"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:94
msgid "Facebook"
msgstr "Facebook"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:95
msgid "Instagram"
msgstr "Instagram"

#: patterns/footer-colophon-3-col.php:57
msgid "Follow"
msgstr "Takip et"

#: patterns/cta-subscribe-centered.php:31
msgctxt "Sample text for Sign Up Button"
msgid "Sign up"
msgstr "Üye ol"

#: patterns/cta-pricing.php:157
msgctxt "Sample heading for the third pricing level"
msgid "Expert"
msgstr "Uzman"

#: patterns/cta-pricing.php:37
msgctxt "Sample heading for the first pricing level"
msgid "Free"
msgstr "Ücretsiz"

#: patterns/cta-pricing.php:11
msgctxt "Name for the pricing pattern"
msgid "Pricing Table"
msgstr "Fiyatlandırma Tablosu"

#: patterns/cta-pricing.php
msgctxt "Pattern title"
msgid "Pricing"
msgstr "Fiyatlandırma"

#: patterns/banner-hero.php:37
msgctxt "Button text of the hero section"
msgid "About us"
msgstr "Hakkımızda"

#: patterns/banner-hero.php
msgctxt "Pattern title"
msgid "Hero"
msgstr "Manşet"

#: patterns/hidden-404.php:10
msgctxt "Heading for a webpage that is not found"
msgid "Page Not Found"
msgstr "Sayfa bulunamadı"

#: patterns/footer-colophon-3-col.php:39
msgid "Contact"
msgstr "İletişim"

#: patterns/cta-pricing.php:203
msgctxt "Button text for the third pricing level"
msgid "Subscribe"
msgstr "Abone ol"

#: patterns/cta-pricing.php:145
msgctxt "Button text for the second pricing level"
msgid "Subscribe"
msgstr "Abone ol"

#: patterns/cta-pricing.php:87
msgctxt "Button text for the first pricing level"
msgid "Subscribe"
msgstr "Abone ol"

#: theme.json
msgctxt "Custom template name"
msgid "Single with Sidebar"
msgstr "Kenar çubuklu tekil"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Başlıksız sayfa"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to white"
msgstr "Dikey sert kalaydan beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard mint to white"
msgstr "Dikey sert naneden beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sage to white"
msgstr "Dikey sert adaçayından beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard rust to white"
msgstr "Dikey sert pastan beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sandstone to white"
msgstr "Dikey sert kumtaşından beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard beige to white"
msgstr "Dikey sert bejden beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to white"
msgstr "Dikey yumuşak kalaydan beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft mint to white"
msgstr "Dikey yumuşak naneden beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sage to white"
msgstr "Dikey yumuşak adaçayından beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft rust to white"
msgstr "Dikey yumuşak pastan beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sandstone to white"
msgstr "Dikey yumuşak kumtaşından beyaza"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft beige to white"
msgstr "Dikey yumuşak bej ila beyaz"

#: theme.json
msgctxt "Duotone name"
msgid "Black and pastel blue"
msgstr "Siyah ve pastel mavi"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sage"
msgstr "Siyah ve adaçayı"

#: theme.json
msgctxt "Duotone name"
msgid "Black and rust"
msgstr "Siyah ve pas"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sandstone"
msgstr "Siyah ve kumtaşı"

#: theme.json
msgctxt "Duotone name"
msgid "Black and white"
msgstr "Siyah ve beyaz"

#: styles/rust.json
msgctxt "Color name"
msgid "Base / 2"
msgstr "Temel / 2"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard rust to beige"
msgstr "Dikey sert pastan beje"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical rust to beige"
msgstr "Dikey pastan beje"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard transparent rust to beige"
msgstr "Dikey şeffaf sert pastan beje"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical transparent rust to beige"
msgstr "Dikey transparan pastan beje"

#: styles/rust.json
msgctxt "Duotone name"
msgid "Dark rust to beige"
msgstr "Koyu pastan beje"

#: styles/rust.json
msgctxt "Style variation name"
msgid "Rust"
msgstr "Pas"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Five"
msgstr "Aksan / Beş"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Four"
msgstr "Aksan / Dört"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Three"
msgstr "Aksan / Üç"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Two"
msgstr "Aksan / İki"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent"
msgstr "Aksan"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to dark gray"
msgstr "Dikey sert kalaydan koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard steel to dark gray"
msgstr "Dikey sert çelikten koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard olive to dark gray"
msgstr "Dikey sert zeytinden koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard cinnamon to dark gray"
msgstr "Dikey sert tarçından koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard walnut to dark gray"
msgstr "Dikey sert cevizden koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard beige to dark gray"
msgstr "Dikey sert bejden koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to dark gray"
msgstr "Dikey yumuşak kalaydan koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft steel to dark gray"
msgstr "Dikey yumuşak çelikten koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft olive to dark gray"
msgstr "Dikey yumuşak zeytinden koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft cinnamon to dark gray"
msgstr "Dikey yumuşak tarçından koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft walnut to dark gray"
msgstr "Dikey yumuşak cevizden koyu griye"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft driftwood to dark gray"
msgstr "Dikey yumuşak odundan koyu griye"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and steel"
msgstr "Koyu gri ve çelik"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and olive"
msgstr "Koyu gri ve zeytin"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and cinnamon"
msgstr "Koyu gri ve tarçın"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and walnut"
msgstr "Koyu gri ve ceviz"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and white"
msgstr "Koyu gri ve beyaz"

#: styles/onyx.json
msgctxt "Style variation name"
msgid "Onyx"
msgstr "Oniks"

#: styles/mint.json
msgctxt "Style variation name"
msgid "Mint"
msgstr "Nane"

#: styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 3"
msgstr "Kontrast / 3"

#: styles/ice.json
msgctxt "Style variation name"
msgid "Ice"
msgstr "Buz"

#: styles/maelstrom.json
msgctxt "Style variation name"
msgid "Maelstrom"
msgstr "Girdap"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ocean"
msgstr "Dikey sert mürekkepten okyanusa"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to slate"
msgstr "Dikey sert okyanustan kayrak taşına"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ice"
msgstr "Dikey sert mürekkepten buza"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to ice"
msgstr "Dikey sert okyanustan buza"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard slate to ice"
msgstr "Dikey sert kayraktan buza"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ice to azure"
msgstr "Dikey sert buzdan gök mavisine"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ocean"
msgstr "Dikey mürekkepten okyanusa"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to slate"
msgstr "Dikey okyanustan kayrak taşına"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ice"
msgstr "Diket mürekkepten buza"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to ice"
msgstr "Dikey okyanustan buza"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical slate to ice"
msgstr "Dikey kayraktan buza"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical azure to ice"
msgstr "Dikey gök mavisinden buza"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Ekstra ekstra büyük"

#: styles/fossil.json styles/maelstrom.json theme.json
msgctxt "Font family name"
msgid "Cardo"
msgstr "Cardo"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Three"
msgstr "Kontrast / Üç"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Two"
msgstr "Kontrast / İki"

#: styles/fossil.json
msgctxt "Style variation name"
msgid "Fossil"
msgstr "Fosil"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Serif"
msgstr "Sistem Serif"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Sans-serif"
msgstr "Sistem Sans-serif"

#: styles/ember.json styles/ice.json styles/maelstrom.json styles/mint.json
msgctxt "Font family name"
msgid "Jost"
msgstr "Jost"

#: styles/ember.json styles/mint.json
msgctxt "Font family name"
msgid "Instrument Sans"
msgstr "Instrument Sans"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json theme.json
msgctxt "Color name"
msgid "Base / Two"
msgstr "Temel / İki"

#: styles/ember.json styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 2"
msgstr "Kontrast / 2"

#: patterns/banner-project-description.php
msgctxt "Pattern title"
msgid "Project description"
msgstr "Proje açıklaması"

#: patterns/cta-content-image-on-right.php:41
msgctxt "Button text of this section"
msgid "Download app"
msgstr "Uygulamayı indir"

#: patterns/cta-content-image-on-right.php:47
msgctxt "Button text of this section"
msgid "How it works"
msgstr "Nasıl çalışır"

#: patterns/cta-pricing.php:18
msgctxt "Sample heading for pricing pattern"
msgid "Our Services"
msgstr "Hizmetlerimiz"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to sable"
msgstr "Dikey sert abanozdan samura"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to taupe"
msgstr "Dikey sert samurdan gri-kahveye"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to beige"
msgstr "Dikey sert abanozdan beje"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to beige"
msgstr "Dikey sert samurdan beje"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard taupe to beige"
msgstr "Dikey sert gri-kahveden beje"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard beige to linen"
msgstr "Dikey sert bejden ketene"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to sable"
msgstr "Dikey abanozdan samura"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to beige"
msgstr "Dikey abanozdan beje"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical sable to beige"
msgstr "Dikey samurdan beje"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical taupe to beige"
msgstr "Dikey gri-kahveden beje"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical linen to beige"
msgstr "Dikey ketenden beje"

#: styles/ember.json
msgctxt "Duotone name"
msgid "Orange and white"
msgstr "Turuncu ve beyaz"

#: styles/ember.json
msgctxt "Style variation name"
msgid "Ember"
msgstr "Kor"

#: patterns/text-title-left-image-right.php:28
msgctxt "Description for the About pattern"
msgid "Leaving an indelible mark on the landscape of tomorrow."
msgstr "Yarının manzarasına silinmez bir iz bırakmak."

#: patterns/text-title-left-image-right.php:21
msgctxt "Headline for the About pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études, kapsamlı danışmanlık, yönetim, tasarım ve araştırma çözümleri sunar. Her mimari girişim, geleceği şekillendirme fırsatıdır."

#: patterns/text-title-left-image-right.php
msgctxt "Pattern title"
msgid "Title text and button on left with image on right"
msgstr "Başlık metni ve düğme sol tarafta, resim sağ tarafta"

#: patterns/text-project-details.php:35 patterns/text-project-details.php:43
msgctxt "Descriptive text for the feature area"
msgid "The revitalized Art Gallery is set to redefine the cultural landscape of Toronto, serving as a nexus of artistic expression, community engagement, and architectural marvel. The expansion and renovation project pay homage to the Art Gallery's rich history while embracing the future, ensuring that the gallery remains a beacon of inspiration."
msgstr "Yeniden canlandırılan sanat galerisi, Toronto'nun kültürel manzarasını yeniden tanımlamaya hazırlanıyor ve sanatsal ifadenin, topluluk katılımının ve mimari harikanın bir bağlantı noktası olarak hizmet ediyor. Genişletme ve yenileme projesi, Sanat Galerisi'nin zengin tarihine saygı duruşunda bulunurken geleceği de kucaklıyor ve galerinin bir ilham kaynağı olarak kalmasını sağlıyor."

#: patterns/text-project-details.php:27
msgctxt "Descriptive title for the feature area"
msgid "With meticulous attention to detail and a commitment to excellence, we create spaces that inspire, elevate, and enrich the lives of those who inhabit them."
msgstr "Detaylara büyük bir titizlikle dikkat etme ve mükemmelliğe olan bağlılığımızla, içinde yaşayanların hayatlarına ilham veren, yükselten ve zenginleştiren alanlar yaratıyoruz."

#: patterns/text-project-details.php:18
msgctxt "Title text for the feature area"
msgid "The revitalized art gallery is set to redefine cultural landscape."
msgstr "Canlandırılmış sanat galerisi, kültürel manzarayı yeniden tanımlıyor."

#: patterns/text-project-details.php
msgctxt "Pattern title"
msgid "Project details"
msgstr "Proje detayları"

#: patterns/text-feature-grid-3-col.php:112
msgctxt "Sample content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Études Mimari Çözümler ile hayal gücü ve uzmanlığın birleşimini deneyimleyin."

#: patterns/text-feature-grid-3-col.php:108
msgctxt "Sample heading"
msgid "Architectural Solutions"
msgstr "Mimari çözümler"

#: patterns/text-feature-grid-3-col.php:96
msgctxt "Sample feature heading"
msgid "Project Management"
msgstr "Proje yönetimi"

#: patterns/text-feature-grid-3-col.php:63
msgctxt "Sample feature heading"
msgid "App Access"
msgstr "Uygulama erişimi"

#: patterns/text-feature-grid-3-col.php:51
msgctxt "Sample feature heading"
msgid "Continuous Support"
msgstr "Sürekli destek"

#: patterns/text-feature-grid-3-col.php:43
#: patterns/text-feature-grid-3-col.php:55
#: patterns/text-feature-grid-3-col.php:67
#: patterns/text-feature-grid-3-col.php:88
#: patterns/text-feature-grid-3-col.php:100
msgctxt "Sample feature content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Études Mimari Çözümler ile hayal gücü ve uzmanlığın birleşimini deneyimleyin."

#: patterns/text-feature-grid-3-col.php:39
msgctxt "Sample feature heading"
msgid "Renovation and restoration"
msgstr "Yenileme ve restorasyon"

#: patterns/text-feature-grid-3-col.php:24
msgctxt "Sub-heading of the features"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Kapsamlı profesyonel hizmet paketimiz, ev sahiplerinden ticari geliştiricilere kadar çok çeşitli müşterilere hitap etmektedir."

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern title"
msgid "Feature grid, 3 columns"
msgstr "Özellik tablosu, 3 sütunlu"

#: patterns/text-centered-statement.php:21
msgid "<em>Études</em> is not confined to the past—we are passionate about the cutting edge designs shaping our world today."
msgstr "<em>Études</em> geçmişle sınırlı değil - dünyamızı bugün şekillendiren son teknoloji tasarımlara tutkulu bir ilgi duyuyoruz."

#: patterns/text-centered-statement.php
msgctxt "Pattern title"
msgid "Centered statement"
msgstr "Ortalanmış ifade"

#. Translators: About text placeholder
#: patterns/text-centered-statement-small.php:23
msgid "I write about finance, management and economy, my book “%1$s” is out now."
msgstr "Finans, yönetim ve ekonomi konularında yazılar yazıyorum, kitabım \"%1$s\" şu anda satışta."

#. Translators: About link placeholder
#: patterns/text-centered-statement-small.php:20
msgid "Money Studies"
msgstr "Para çalışmaları"

#: patterns/text-centered-statement-small.php
msgctxt "Pattern title"
msgid "Centered statement, small"
msgstr "Ortalanmış ifade, küçük"

#: patterns/text-alternating-images.php:97
msgctxt "Sample list item"
msgid "A world of thought-provoking articles."
msgstr "Düşünce provokasyonu yaratan makaleler dünyası."

#: patterns/text-alternating-images.php:91
msgctxt "Sample heading"
msgid "Études Newsletter"
msgstr "Études Bülteni"

#: patterns/text-alternating-images.php:82
msgid "Windows of a building in Nuremberg, Germany"
msgstr "Nürnberg, Almanya'daki bir binanın pencereleri"

#: patterns/text-alternating-images.php:64
msgid "Tourist taking photo of a building"
msgstr "Bir binanın fotoğrafını çeken turist"

#: patterns/text-alternating-images.php:37
msgctxt "Sample list heading"
msgid "Études Architect App"
msgstr "Études mimar uygulaması"

#: patterns/text-alternating-images.php:23
msgctxt "Sample subheading content"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Kapsamlı profesyonel hizmet yelpazemiz, ev sahiplerinden ticari geliştiricilere kadar geniş bir müşteri kitlesine hitap ediyor."

#: patterns/text-alternating-images.php:19
msgctxt "Sample heading content"
msgid "An array of resources"
msgstr "Bir dizi kaynak"

#: patterns/text-alternating-images.php
msgctxt "Pattern title"
msgid "Text with alternating images"
msgstr "Alternatif görseller içeren metin"

#: patterns/testimonial-centered.php:39
msgctxt "Designation of Person Provided Testimonial"
msgid "CEO, Greenprint"
msgstr "CEO, Greenprint"

#: patterns/testimonial-centered.php:35
msgctxt "Name of Person Provided the Testimonial"
msgid "Annie Steiner"
msgstr "Annie Steiner"

#: patterns/testimonial-centered.php:26
msgctxt "Name of testimonial citation group"
msgid "Testimonial source"
msgstr "Görüş kaynağı"

#: patterns/testimonial-centered.php:18
msgctxt "Testimonial Text or Review Text Got From the Person"
msgid "“Études has saved us thousands of hours of work and has unlocked insights we never thought possible.”"
msgstr "Études bizi binlerce saatlik çalışmadan kurtardı ve mümkün olduğunu asla düşünmediğimiz içgörülerin kilidini açtı."

#: patterns/testimonial-centered.php
msgctxt "Pattern title"
msgid "Centered testimonial"
msgstr "Ortalanmış görüş"

#: patterns/template-single-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio single post template"
msgstr "Portföy tekil yazı şablonu"

#: patterns/template-search-blogging.php
msgctxt "Pattern title"
msgid "Blogging search template"
msgstr "Blog arama şablonu"

#: patterns/template-search-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio search template"
msgstr "Portföy arama şablonu"

#: patterns/template-index-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio index template"
msgstr "Portföy dizini şablonu"

#: patterns/template-index-blogging.php
msgctxt "Pattern title"
msgid "Blogging index template"
msgstr "Blog dizini şablonu"

#: patterns/template-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home template with post featured images"
msgstr "Yazı öne çıkan görselleri içeren portföy ana sayfası şablonu"

#: patterns/template-home-business.php
msgctxt "Pattern title"
msgid "Business home template"
msgstr "İşletme ana sayfası şablonu"

#: patterns/template-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home template"
msgstr "Blog ana sayfası şablonu"

#: patterns/template-archive-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio archive template"
msgstr "Portföy arşiv şablonu"

#: patterns/template-archive-blogging.php
msgctxt "Pattern title"
msgid "Blogging archive template"
msgstr "Blog arşiv şablonu"

#: patterns/team-4-col.php:121
msgctxt "Sample role of a team member"
msgid "Project Manager"
msgstr "Proje yöneticisi"

#: patterns/team-4-col.php:116
msgctxt "Sample name of a team member"
msgid "Ivan Lawrence"
msgstr "Ivan Lawrence"

#: patterns/team-4-col.php:92
msgctxt "Sample name of a team member"
msgid "Helga Steiner"
msgstr "Helga Steiner"

#: patterns/team-4-col.php:73
msgctxt "Sample role of a team member"
msgid "Engineering Manager"
msgstr "Mühendislik yöneticisi"

#: patterns/team-4-col.php:68
msgctxt "Sample name of a team member"
msgid "Rhye Moore"
msgstr "Rhye Moore"

#: patterns/team-4-col.php:49
msgctxt "Sample role of a team member"
msgid "Founder, CEO & Architect"
msgstr "Kurucu, CEO & Mimar"

#: patterns/team-4-col.php:44
msgctxt "Sample name of a team member"
msgid "Francesca Piovani"
msgstr "Francesca Piovani"

#: patterns/team-4-col.php:20
msgctxt "Sample descriptive text of the team pattern"
msgid "Our comprehensive suite of professionals caters to a diverse team, ranging from seasoned architects to renowned engineers."
msgstr "Kapsamlı profesyonel ekibimiz, deneyimli mimarlardan ünlü mühendislere kadar geniş bir gruba hitap etmektedir."

#: patterns/team-4-col.php:16
msgctxt "Sample heading for the team pattern"
msgid "Meet our team"
msgstr "Ekibimizle tanışın"

#: patterns/team-4-col.php:11
msgctxt "Name of team pattern"
msgid "Team members"
msgstr "Ekip üyeleri"

#: patterns/team-4-col.php
msgctxt "Pattern title"
msgid "Team members, 4 columns"
msgstr "Ekip üyeleri, 4 sütun"

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern title"
msgid "Posts with featured images only, 3 columns"
msgstr "Sadece öne çıkan görseli olan yazılar, 3 sütun"

#: patterns/posts-grid-2-col.php:14 patterns/posts-list.php:14
#: patterns/template-index-blogging.php:16
msgid "Watch, Read, Listen"
msgstr "İzle, oku, dinle"

#: patterns/posts-3-col.php
msgctxt "Pattern title"
msgid "List of posts, 3 columns"
msgstr "Yazı listesi, 3 sütun"

#: patterns/posts-1-col.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Yazı listesi, 1 sütun"

#: patterns/page-rsvp-landing.php:49
msgid "Green staircase at Western University, London, Canada"
msgstr "Western Universitesi, Londra, Kanada'daki yeşil merdivenler"

#: patterns/page-rsvp-landing.php:14
msgctxt "Name of RSVP landing page pattern"
msgid "RSVP Landing Page"
msgstr "LCV açılış sayfası"

#: patterns/page-rsvp-landing.php
msgctxt "Pattern title"
msgid "RSVP landing"
msgstr "LCV açılış"

#: patterns/page-portfolio-overview.php
msgctxt "Pattern title"
msgid "Portfolio project overview"
msgstr "Portföy projesi genel görünümü"

#: patterns/page-newsletter-landing.php:29
msgctxt "sample content for newsletter subscription"
msgid "Subscribe to the newsletter and stay connected with our community"
msgstr "Bültene abone olun ve topluluğumuzla bağlantıda kalın"

#: patterns/page-newsletter-landing.php
msgctxt "Pattern title"
msgid "Newsletter landing"
msgstr "Bülten açılışı"

#: patterns/page-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home with post featured images"
msgstr "Öne çıkan görseller ile portföy ana sayfası"

#: patterns/page-home-business.php
msgctxt "Pattern title"
msgid "Business home"
msgstr "İşletme ana sayfası"

#: patterns/page-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home"
msgstr "Blog ana sayfası"

#: patterns/hidden-sidebar.php:72
msgid "Search the website"
msgstr "Web sitesinde ara"

#: patterns/hidden-sidebar.php:60
msgid "Financial apps for families"
msgstr "Aileler için finansal uygulamalar"

#: patterns/hidden-sidebar.php:59
msgid "Latest inflation report"
msgstr "Son enflasyon raporu"

#: patterns/hidden-sidebar.php:53
msgid "Links I found useful and wanted to share."
msgstr "İşe yarar bulduğum ve paylaşmak istediğim bağlantılar."

#: patterns/hidden-sidebar.php:17
msgid "About the author"
msgstr "Yazar hakkında"

#: patterns/hidden-post-meta.php
msgctxt "Pattern title"
msgid "Post meta"
msgstr "Yazı meta bilgisi"

#: patterns/hidden-portfolio-hero.php
msgctxt "Pattern title"
msgid "Portfolio hero"
msgstr "Portföy manşet alanı"

#: patterns/hidden-404.php:13
msgctxt "Message to convey that a webpage could not be found"
msgid "The page you are looking for does not exist, or it has been moved. Please try searching using the form below."
msgstr "Aradığınız sayfa mevcut değil veya taşınmış. Lütfen aşağıdaki formu kullanarak arama yapmayı deneyin."

#: patterns/gallery-project-layout.php:54
msgid "Art Gallery of Ontario, Toronto, Canada"
msgstr "Ontario, Toronto, Kanada sanat galerisi"

#: patterns/gallery-project-layout.php:49
msgctxt "Sample text for the feature area"
msgid "2. Case studies that celebrate the artistry can fuel curiosity and ignite inspiration."
msgstr "2. Sanatçılığı öven vaka çalışmaları merakı körükleyebilir ve ilhamı ateşleyebilir."

#: patterns/gallery-project-layout.php:38
msgctxt "Sample text for the feature area"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers. With a commitment to innovation and sustainability, Études is the bridge that transforms architectural dreams into remarkable built realities."
msgstr "Kapsamlı profesyonel hizmet paketimiz, ev sahiplerinden ticari geliştiricilere kadar çok çeşitli müşterilere hitap etmektedir. Yenilik ve sürdürülebilirliğe olan bağlılığıyla Études, mimari hayalleri dikkat çekici gerçek yapılara dönüştüren köprüdür."

#: patterns/gallery-project-layout.php:26
msgctxt "Sample text for the feature area"
msgid "1. Through Études, we aspire to redefine architectural boundaries and usher in a new era of design excellence that leaves an indelible mark on the built environment."
msgstr "1. Études aracılığıyla, mimari sınırları yeniden tanımlamayı ve yapısal çevrede silinmez bir iz bırakan yeni bir tasarım mükemmelliği çağını başlatmayı amaçlıyoruz."

#: patterns/gallery-project-layout.php:21
msgid "An empty staircase under an angular roof in Darling Harbour, Sydney, Australia"
msgstr "Darling Limanı, Sidney, Avustralya'da açılı bir çatının altında boş bir merdiven"

#: patterns/gallery-project-layout.php
msgctxt "Pattern title"
msgid "Project layout"
msgstr "Proje yerleşimi"

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern title"
msgid "Full screen image"
msgstr "Tam ekran görsel"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer with colophon, 4 columns"
msgstr "Kolofonlu alt kısım, 4 sütun"

#: patterns/footer-colophon-3-col.php:82
msgid "&copy;"
msgstr "&copy;"

#: patterns/footer-colophon-3-col.php:42
msgctxt "Example email in site footer"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/footer-colophon-3-col.php:30
msgid "Keep up, get in touch."
msgstr "Takipte kalın, iletişime geçin."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern title"
msgid "Footer with colophon, 3 columns"
msgstr "Kolofonlu alt kısım, 3 sütun"

#. Translators: Designed with WordPress
#: patterns/footer-centered-logo-nav.php:25
#: patterns/footer-colophon-3-col.php:94 patterns/footer.php:120
msgid "Designed with %1$s"
msgstr "%1$s ile tasarlandı"

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern title"
msgid "Footer with centered logo and navigation"
msgstr "Ortalanmış logo ve dolaşım menüsü ile alt kısım"

#: patterns/cta-subscribe-centered.php:24
msgctxt "Sample text for Subscriber Description"
msgid "Stay in the loop with everything you need to know."
msgstr "Bilmeniz gereken her şeyle güncel kalın."

#: patterns/cta-subscribe-centered.php:20
msgctxt "Sample text for Subscriber Heading with numbers"
msgid "Join 900+ subscribers"
msgstr "900+ aboneye siz de katılın"

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern title"
msgid "Centered call to action"
msgstr "Ortalanmış harekete geçirici mesaj"

#: patterns/cta-services-image-left.php:39
msgctxt "Sample button text to view the services"
msgid "Our services"
msgstr "Hizmetlerimiz"

#: patterns/cta-services-image-left.php:32
msgctxt "Sample description of the services pattern"
msgid "Experience the fusion of imagination and expertise with Études—the catalyst for architectural transformations that enrich the world around us."
msgstr "Etrafımızdaki dünyayı zenginleştiren mimari dönüşümlerin katalizörü olan Études ile hayal gücü ve uzmanlığın birleşimini deneyimleyin."

#: patterns/cta-services-image-left.php:28
msgctxt "Sample heading of the services pattern"
msgid "Guiding your business through the project"
msgstr "Proje boyunca işletmenize rehberlik"

#: patterns/cta-services-image-left.php
msgctxt "Pattern title"
msgid "Services call to action with image on left"
msgstr "Solda resimle birlikte hizmetler harekete geçirici mesajı"

#: patterns/cta-rsvp.php:50 patterns/text-title-left-image-right.php:51
msgid "A ramp along a curved wall in the Kiasma Museu, Helsinki, Finland"
msgstr "Kiasma Müzesi'nde kavisli bir duvar boyunca bir rampa, Helsinki, Finlandiya"

#: patterns/cta-rsvp.php:34 patterns/page-rsvp-landing.php:34
msgctxt "Call to action button text for the reservation button"
msgid "Reserve your spot"
msgstr "Yerinizi ayırtın"

#: patterns/cta-rsvp.php:27 patterns/page-rsvp-landing.php:28
msgctxt "RSVP call to action description"
msgid "Experience the fusion of imagination and expertise with Études Arch Summit, February 2025."
msgstr "Şubat 2025'teki Études Arch Zirvesi ile hayal gücü ve uzmanlığın birleşimini deneyimleyin."

#: patterns/cta-rsvp.php:21 patterns/page-rsvp-landing.php:23
msgctxt "Initials for ´please respond´"
msgid "RSVP"
msgstr "LCV"

#: patterns/cta-rsvp.php:11
msgctxt "Name of RSVP pattern"
msgid "RSVP"
msgstr "LCV"

#: patterns/cta-rsvp.php
msgctxt "Pattern title"
msgid "RSVP"
msgstr "LCV"

#: patterns/cta-pricing.php:189
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android"
msgstr "iOS ve Android için <em>Études</em> uygulamasına özel erişim"

#: patterns/cta-pricing.php:173
msgctxt "Feature for pricing level"
msgid "Exclusive, unlimited access to <em>Études Articles</em>."
msgstr "<em>Etudes Makalelerine</em> özel, sınırsız erişim."

#: patterns/cta-pricing.php:162
msgctxt "Sample price for the third pricing level"
msgid "$28"
msgstr "28$"

#: patterns/cta-pricing.php:115
msgctxt "Feature for pricing level"
msgid "Access to 20 exclusive <em>Études Articles</em> per month."
msgstr "Aylık 20 özel <em>Etudes Makalesine</em> erişim."

#: patterns/cta-pricing.php:104
msgctxt "Sample price for the second pricing level"
msgid "$12"
msgstr "12$"

#: patterns/cta-pricing.php:99
msgctxt "Sample heading for the second pricing level"
msgid "Connoisseur"
msgstr "Uzman"

#: patterns/cta-pricing.php:72 patterns/cta-pricing.php:131
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android."
msgstr "iOS ve Android için <em>Études</em> uygulamasına özel erişim."

#: patterns/cta-pricing.php:62 patterns/cta-pricing.php:123
#: patterns/cta-pricing.php:181
msgctxt "Feature for pricing level"
msgid "Weekly print edition."
msgstr "Haftalık basılı sürüm"

#: patterns/cta-pricing.php:53
msgctxt "Feature for pricing level"
msgid "Access to 5 exclusive <em>Études Articles</em> per month."
msgstr "Ayda 5 özel <em>Etudes Makalesine</em> erişim."

#: patterns/cta-pricing.php:42
msgctxt "Sample price for the first pricing level"
msgid "$0"
msgstr "0$"

#: patterns/cta-pricing.php:22
msgctxt "Sample description for a pricing table"
msgid "We offer flexible options, which you can adapt to the different needs of each project."
msgstr "Her projenin farklı ihtiyaçlarına göre uyarlayabileceğiniz esnek seçenekler sunuyoruz."

#: patterns/cta-content-image-on-right.php:59
#: patterns/cta-services-image-left.php:19
msgid "White abstract geometric artwork from Dresden, Germany"
msgstr "Dresden, Almanya'dan beyaz soyut geometrik sanat eserleri"

#: patterns/cta-content-image-on-right.php:32
#: patterns/text-alternating-images.php:52
msgctxt "Sample list item"
msgid "Experience the world of architecture."
msgstr "Mimarlık dünyasını deneyimleyin."

#: patterns/cta-content-image-on-right.php:28
#: patterns/text-alternating-images.php:48
msgctxt "Sample list item"
msgid "Showcase your projects."
msgstr "Projelerinizi tanıtın."

#: patterns/cta-content-image-on-right.php:24
#: patterns/text-alternating-images.php:44
msgctxt "Sample list item"
msgid "Collaborate with fellow architects."
msgstr "Diğer mimarlarla işbirliği yapın."

#: patterns/cta-content-image-on-right.php:18
msgctxt "Sample heading"
msgid "Enhance your architectural journey with the Études Architect app."
msgstr "Études Architect uygulamasıyla mimari yolculuğunuzu geliştirin."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern title"
msgid "Call to action with image on right"
msgstr "Sağda görsel ile harekete geçirici mesaj"

#: patterns/banner-project-description.php:41
msgid "Hyatt Regency San Francisco, San Francisco, United States"
msgstr "Hyatt Regency San Francisco, San Francisco, Amerika Birleşik Devletleri"

#: patterns/banner-project-description.php:26
msgctxt "Sample descriptive text for a project or post."
msgid "This transformative project seeks to enhance the gallery's infrastructure, accessibility, and exhibition spaces while preserving its rich cultural heritage."
msgstr "Bu dönüştürücü proje, galerinin zengin kültürel mirasını korurken altyapısını, erişilebilirliğini ve sergi alanlarını geliştirmeyi amaçlıyor."

#: patterns/banner-project-description.php:17
msgctxt "Sample title for a project or post"
msgid "Art Gallery — Overview"
msgstr "Sanat galerisi — Genel görünüm"

#: patterns/banner-hero.php:52
msgid "Building exterior in Toronto, Canada"
msgstr "Toronto, Kanada'da bina cephesi"

#: patterns/banner-hero.php:26
msgctxt "Content of the hero section"
msgid "Études is a pioneering firm that seamlessly merges creativity and functionality to redefine architectural excellence."
msgstr "Études, mimari mükemmelliği yeniden tanımlamak için yaratıcılığı ve işlevselliği kusursuz bir şekilde birleştiren öncü bir firmadır."

#: patterns/banner-hero.php:18
msgctxt "Heading of the hero section"
msgid "A commitment to innovation and sustainability"
msgstr "Yenilik ve sürdürülebilirliğe bağlılık"

#: patterns/text-feature-grid-3-col.php:16
msgctxt "Heading of the features"
msgid "A passion for creating spaces"
msgstr "Mekan yaratma tutkusu"

#: patterns/text-alternating-images.php:105
msgctxt "Sample list item"
msgid "Exclusive access to design insights."
msgstr "Tasarım iç görülerine özel erişim."

#: patterns/text-alternating-images.php:101
msgctxt "Sample list item"
msgid "Case studies that celebrate architecture."
msgstr "Mimarlığı göklere çıkaran vaka çalışmaları."

#: patterns/posts-grid-2-col.php
msgctxt "Pattern title"
msgid "Grid of posts featuring the first post, 2 columns"
msgstr "İlk yazıyı çöne çıkartan yazı ızgarası, 2 sütun"

#: patterns/page-home-portfolio-gallery.php
msgctxt "Pattern title"
msgid "Portfolio home image gallery"
msgstr "Portföy ana sayfası görsel galerisi"

#: patterns/hidden-post-meta.php:25
msgctxt "Prefix for the post category block: in category name"
msgid "in "
msgstr "kategori: "

#: patterns/hidden-post-meta.php:20
msgctxt "Prefix for the post author block: By author name"
msgid "by"
msgstr "yazar:"

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 4 columns"
msgstr "Dağınık galeri, 4 sütunlu"

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 3 columns"
msgstr "Dağınık galeri, 3 sütunlu"

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 2 columns"
msgstr "Dağınık galeri, 2 sütunlu"

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern title"
msgid "Offset posts with featured images only, 4 columns"
msgstr "Sadece öne çıkan görsel ile dağınık yazılar, 4 sütunlu"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Wide Image"
msgstr "Geniş görsel ile sayfa"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Sidebar"
msgstr "Kenar çubuğu ile sayfa"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress ekibi"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfour/"
msgstr "https://wordpress.org/themes/twentytwentyfour/"